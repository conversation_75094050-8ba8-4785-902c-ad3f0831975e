//
//  ScanView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/15.
//

import SwiftUI

struct ScanView: View {
    @State private var isScanning = false
    @EnvironmentObject var cardStore: CardStore

    var body: some View {
        NavigationStack {
            ZStack{
                CustomAngularGradient()
                
                ScrollView{
                    VStack(spacing: Theme.Spacing.xl) {
                        // 创建更多卡片
                        NavigationLink{
                            ImageProcessView()
                        } label:{
                            CardPreviewSection()
                        }
                        
                        // 卡片列表
                        ItemCardLibrary()
                        
                    }
                    .padding(.top, Theme.Spacing.lg)
                    .padding(.horizontal, Theme.Spacing.md)
                    .padding(.bottom, Theme.Spacing.tab)
                }
                .scrollContentBackground(.hidden)
            }
            .toolbarBackground(.clear, for: .navigationBar)
            .toolbarColorScheme(.dark, for: .navigationBar)
        }
    }
}

// MARK: - AR Placeholder Card
struct ARPlaceholderCard: View {
    var body: some View {
        VStack(spacing: Theme.Spacing.md) {
            Image(systemName: "arkit")
                .font(.system(size: 40))
                .foregroundColor(.skyBlue)

            Text("扫描商品碳影响")
                .font(.title3Brand)
                .foregroundColor(.textPrimary)

            Text("未来将融入AR功能，敬请期待")
                .font(.bodyBrand)
                .foregroundColor(.textSecondary)
                .multilineTextAlignment(.center)
        }
        .padding(Theme.Spacing.xl)
        .glassCard()
    }
}

// MARK: - Scan Button
struct ScanButton: View {
    @Binding var isScanning: Bool

    var body: some View {
        Button(action: toggleScanning) {
            HStack {
                Image(systemName: isScanning ? "stop.circle.fill" : "qrcode.viewfinder")
                    .foregroundColor(.textPrimary)

                Text(isScanning ? "停止扫描" : "开始扫描")
                    .font(.bodyBrand)
                    .foregroundColor(.textPrimary)
            }
            .frame(maxWidth: .infinity)
        }
        .buttonStyle(PrimaryButtonStyle())
    }

    private func toggleScanning() {
        withAnimation(Theme.AnimationStyle.bouncy) {
            isScanning.toggle()
        }
    }
}



// MARK: - Card Preview Section
struct CardPreviewSection: View {
    @EnvironmentObject var cardStore: CardStore

    // 获取最近的两张卡片
    private var recentCards: [ItemCard] {
        Array(cardStore.cards.sorted { $0.createdAt > $1.createdAt }.prefix(2))
    }

    var body: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.md) {
            HStack {
                Text("最近卡片")
                    .font(.title3Brand)
                    .foregroundColor(.textPrimary)

                Spacer()

                NavigationLink(destination: ImageProcessView()) {
                    HStack(spacing: 4) {
                        Text("创建更多")
                            .font(.captionBrand)
                            .foregroundColor(.brandGreen)

                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.brandGreen)
                    }
                }
            }

            if recentCards.isEmpty {
                // 空状态
                NavigationLink(destination: ImageProcessView()) {
                    HStack {
                        VStack(spacing: Theme.Spacing.sm) {
                            Image(systemName: "folder")
                                .font(.title2)
                                .foregroundColor(.textSecondary)

                            Text("暂无卡片")
                                .font(.bodyBrand)
                                .foregroundColor(.textSecondary)

                            Text("使用图像处理功能创建您的第一张卡片")
                                .font(.captionBrand)
                                .foregroundColor(.textSecondary)
                                .multilineTextAlignment(.center)
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, Theme.Spacing.lg)
                    }
                    .background(Color.cardBackground.opacity(0.1))
                    .cornerRadius(Theme.CornerRadius.lg)
                    .glassCard()
                }
                .buttonStyle(PlainButtonStyle())
            } else {
                // 卡片预览
                HStack(spacing: Theme.Spacing.sm) {
                    ForEach(recentCards) { card in
                            CompactCardThumbnailView(card: card)
//                        .buttonStyle(PlainButtonStyle())
                    }

                    // 如果只有一张卡片，添加占位符
                    if recentCards.count == 1 {
                        NavigationLink(destination: ImageProcessView()) {
                            PlaceholderCardView()
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
            }
        }
        .padding(Theme.Spacing.lg)
    }
}

// MARK: - Compact Card Thumbnail View
struct CompactCardThumbnailView: View {
    let card: ItemCard

    var body: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.xs) {
            // 图像
            if let image = card.image {
                Image(uiImage: image)
                    .resizable()
                    .scaledToFill()
                    .frame(height: 80)
                    .clipped()
                    .cornerRadius(Theme.CornerRadius.sm)
            } else {
                Rectangle()
                    .fill(Color.cardBackground.opacity(0.3))
                    .frame(height: 80)
                    .cornerRadius(Theme.CornerRadius.sm)
                    .overlay(
                        Image(systemName: "photo")
                            .font(.title3)
                            .foregroundColor(.textSecondary)
                    )
            }

            // 标题
            Text(card.title)
                .font(.caption)
                .foregroundColor(.textPrimary)
                .lineLimit(2)
                .multilineTextAlignment(.leading)

            // 卡片类型标签
            Text(card.cardType.displayName)
                .font(.caption2)
                .foregroundColor(.textPrimary)
                .padding(.horizontal, 4)
                .padding(.vertical, 2)
                .background(Color.brandGreen.opacity(0.2))
                .cornerRadius(Theme.CornerRadius.sm)
        }
        .frame(maxWidth: .infinity)
        .padding(Theme.Spacing.xs)
        .background(Color.cardBackground.opacity(0.1))
        .cornerRadius(Theme.CornerRadius.md)
        .glassCard()
    }
}

// MARK: - Placeholder Card View
struct PlaceholderCardView: View {
    var body: some View {
        VStack(spacing: Theme.Spacing.sm) {
            Rectangle()
                .fill(Color.cardBackground.opacity(0.2))
                .frame(height: 80)
                .cornerRadius(Theme.CornerRadius.sm)
                .overlay(
                    VStack(spacing: 4) {
                        Image(systemName: "plus")
                            .font(.title3)
                            .foregroundColor(.textSecondary)

                        Text("创建更多")
                            .font(.caption2)
                            .foregroundColor(.textSecondary)
                    }
                )

            Text("点击创建")
                .font(.caption)
                .foregroundColor(.textSecondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding(Theme.Spacing.xs)
        .background(Color.cardBackground.opacity(0.05))
        .cornerRadius(Theme.CornerRadius.md)
        .overlay(
            RoundedRectangle(cornerRadius: Theme.CornerRadius.md)
                .stroke(Color.textSecondary.opacity(0.3), style: StrokeStyle(lineWidth: 1, dash: [5]))
        )
    }
}

#Preview {
    let cardStore = CardStore()
    ScanView()
        .environmentObject(cardStore)
        .stableBackground()
}
