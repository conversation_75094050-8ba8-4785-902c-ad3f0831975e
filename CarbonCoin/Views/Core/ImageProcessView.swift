//
//  ImageProcessView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/22.
//

import SwiftUI
import PhotosUI
import Photos
import PopupView

struct ImageProcessView: View {
    @StateObject private var viewModel = ImageProcessViewModel()
    @EnvironmentObject var cardStore: CardStore
    @State private var showCamera = false
    @State private var showCardCreatedAlert = false
    @State private var isCroppingProcessed = false
    @State private var isCroppingInput = false
    @StateObject private var locationManager = UserLocationManager()
    @StateObject private var locationViewModel = LocationViewModel()

    // 新增状态变量
    @State private var isCreatingCard = false // 卡片创建加载状态
    @State private var showSuccessPopup = false // 成功提示弹窗
    @State private var createdCardTitle = "" // 创建成功的卡片标题

    var body: some View {
        ZStack {
            CustomAngularGradient()
            
            NavigationStack {
                VStack(spacing: 20) {
                    // 根据当前步骤显示不同内容
                    switch viewModel.currentStep {
                    case .cardTypeSelection:
                        cardTypeSelectionView
                        
                    case .imageSelection:
                        imageSelectionView
                        
                    case .subjectSelection:
                        subjectSelectionView
                        
                    case .imageAnalysis:
                        imageAnalysisView
                    }
                    
                    Spacer(minLength: Theme.Spacing.tab)
                }
                
                
                .padding()
                .navigationTitle("创建更多")
                .navigationBarTitleDisplayMode(.inline)
                .sheet(isPresented: $showCamera) {
                    ImagePicker(selectedImage: $viewModel.inputImage)
                }
                .photosPicker(isPresented: $viewModel.showPhotoPicker, selection: $viewModel.selectedPhoto, matching: .images)
                
                .alert("卡片创建成功", isPresented: $showCardCreatedAlert) {
                    Button("确定", role: .cancel) { }
                } message: {
                    Text("物品卡片已保存到卡片库中")
                }
                .alert("需要相册权限", isPresented: $viewModel.showPermissionAlert) {
                    Button("前往设置") {
                        if let settingsURL = URL(string: UIApplication.openSettingsURLString) {
                            UIApplication.shared.open(settingsURL)
                        }
                    }
                    Button("取消", role: .cancel) { }
                } message: {
                    Text("请在设置中授予相册访问权限以选择图片")
                }
                .overlay {
                    if viewModel.isProcessing {
                        ProgressView()
                            .scaleEffect(2)
                    }
                }
                // 卡片创建加载状态覆盖层
                .overlay {
                    if isCreatingCard {
                        ZStack {
                            Color.black.opacity(0.3)
                                .ignoresSafeArea()
                            
                            VStack(spacing: Theme.Spacing.md) {
                                ProgressView()
                                    .scaleEffect(1.5)
                                    .tint(.brandGreen)
                                
                                Text("正在创建卡片...")
                                    .font(.bodyBrand)
                                    .foregroundColor(.textPrimary)
                            }
                            .padding(Theme.Spacing.xl)
                            .background(
                                RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                                    .fill(Color.cardBackground)
                            )
                        }
                    }
                }
                // 成功提示弹窗
                .popup(isPresented: $showSuccessPopup) {
                    CardCreatedSuccessPopup(cardTitle: createdCardTitle)
                } customize: {
                    $0
                        .type(.floater(verticalPadding: 20, useSafeAreaInset: true))
                        .position(.top)
                        .animation(.spring())
                        .closeOnTapOutside(true)
                        .autohideIn(3)
                }
                // 监听 selectedPhoto 变化以触发加载
                .onChange(of: viewModel.selectedPhoto) {
                    Task {
                        await viewModel.loadImage()
                    }
                }
                // 监听 inputImage 变化以重新检测主体（用于图像编辑后）
                .onChange(of: viewModel.inputImage) {
                    // 使用 DispatchQueue.main.async 确保在主线程中执行
                    DispatchQueue.main.async {
                        viewModel.reDetectSubjects()
                    }
                }
            }
        }
    }

    // MARK: - 创建卡片方法
    private func createItemCard() {
        guard let result = viewModel.analysisResult,
              let processedImage = viewModel.processedImage,
              let imageData = processedImage.pngData(),
              let selectedCardType = viewModel.selectedCardType else {
            return
        }

        // 异步创建卡片，包含位置信息获取
        Task {
            // 开始加载状态
            await MainActor.run {
                isCreatingCard = true
                createdCardTitle = result.Title
            }

            // 获取位置信息
            let (location, locationString) = await getLocationInfoSafely()

            // 创建卡片
            let cardId = await cardStore.saveCard(
                cardType: selectedCardType,
                themeColor: viewModel.extractedThemeColor,
                tags: result.Tags,
                description: result.Description,
                title: result.Title,
                imageData: imageData,
                location: locationString,
                latitude: location?.coordinate.latitude,
                longitude: location?.coordinate.longitude,
                remark: nil // 备注现在由 UserItemCard 管理
            )

            // 显示成功提示
            await MainActor.run {
                isCreatingCard = false
                if cardId != nil {
                    showSuccessPopup = true
                } else {
                    // 处理创建失败的情况
                    print("卡片创建失败")
                }
            }
        }
    }

    // 注意：updateCardLocation 方法已移除，现在在 saveCard 中直接处理位置信息

    // 安全获取位置信息，不阻塞UI
    private func getLocationInfoSafely() async -> (CLLocation?, String) {
        // 优先使用 LocationViewModel 的方法，它更稳定
        let (location, locationString) = await locationViewModel.userLocationManager.getCurrentLocationInfo()

        // 如果 LocationViewModel 获取成功，直接返回
        if let location = location {
            return (location, locationString)
        }

        // 如果 LocationViewModel 失败，尝试使用本地 locationManager 作为备用
        if let currentLoc = locationManager.currentLocation,
           let currentLocString = locationManager.currentLocationString {
            return (currentLoc, currentLocString)
        }

        // 检查权限状态
        guard locationManager.authorizationStatus.isAuthorized else {
            return (nil, "位置权限未授权")
        }

        // 最后尝试获取位置，但设置超时
        return await withTaskGroup(of: (CLLocation?, String).self) { group in
            // 添加位置获取任务
            group.addTask {
                await self.locationManager.getCurrentLocationInfo()
            }

            // 添加超时任务
            group.addTask {
                try? await Task.sleep(nanoseconds: 5_000_000_000) // 增加到5秒超时
                return (nil, "位置获取超时")
            }

            // 返回第一个完成的结果
            if let result = await group.next() {
                group.cancelAll()
                return result
            }

            return (nil, "位置获取失败")
        }
    }

    // MARK: - 子视图
    private var imageDisplayArea: some View {
        GeometryReader { geometry in
            Group {
                VStack {
                    if let processedImage = viewModel.processedImage {
                        // 显示处理后的图片，支持编辑
                        Image(uiImage: processedImage)
                            .resizable()
                            .scaledToFit()
                            .frame(maxHeight: 400)
                            .croppable($isCroppingProcessed, image: Binding(
                                get: { viewModel.processedImage },
                                set: { newImage in
                                    viewModel.processedImage = newImage
                                }
                            ))
                    } else if let inputImage = viewModel.inputImage {
                        // 显示原始图片，支持点击选择主体和编辑
                        ZStack {
                            Image(uiImage: inputImage)
                                .resizable()
                                .scaledToFit()
                                .frame(maxHeight: 400)
                                .croppable($isCroppingInput, image: Binding(
                                    get: { viewModel.inputImage },
                                    set: { newImage in
                                        viewModel.inputImage = newImage
                                    }
                                ))
                                .onTapGesture { location in
                                    // 将点击位置转换为标准化坐标 (0-1)
                                    let imageFrame = getImageFrame(in: geometry, for: inputImage)
                                    let normalizedPoint = CGPoint(
                                        x: (location.x - imageFrame.minX) / imageFrame.width,
                                        y: (location.y - imageFrame.minY) / imageFrame.height
                                    )
                                    viewModel.handleImageTap(at: normalizedPoint)
                                }

                            // 显示所有主体的选择指示器
                            if viewModel.showSubjectSelection {
                                let imageFrame = getImageFrame(in: geometry, for: inputImage)
                                ForEach(viewModel.subjects, id: \.index) { subject in
                                    SubjectIndicator(
                                        subject: subject,
                                        isSelected: viewModel.selectedSubjectIndices.contains(subject.index),
                                        imageFrame: imageFrame
                                    ) {
                                        viewModel.toggleSubjectSelection(subject.index)
                                    }
                                }
                            }
                        }
                    } else {
                        Image(systemName: "photo.on.rectangle")
                            .font(.system(size: 100))
                            .foregroundColor(.gray)
                            .frame(maxHeight: 400)
                    }
                }
            }
            .frame(maxWidth: .infinity)
            .background(Color(.systemGroupedBackground))
            .cornerRadius(12)
        }
    }

    // MARK: - 辅助方法
    private func getImageFrame(in geometry: GeometryProxy, for image: UIImage) -> CGRect {
        let imageAspectRatio = image.size.width / image.size.height
        let containerAspectRatio = geometry.size.width / geometry.size.height

        let imageSize: CGSize
        if imageAspectRatio > containerAspectRatio {
            // 图片更宽，以宽度为准
            imageSize = CGSize(
                width: geometry.size.width,
                height: geometry.size.width / imageAspectRatio
            )
        } else {
            // 图片更高，以高度为准
            let maxHeight: CGFloat = 400
            let actualHeight = min(maxHeight, geometry.size.height)
            imageSize = CGSize(
                width: actualHeight * imageAspectRatio,
                height: actualHeight
            )
        }

        return CGRect(
            x: (geometry.size.width - imageSize.width) / 2,
            y: (geometry.size.height - imageSize.height) / 2,
            width: imageSize.width,
            height: imageSize.height
        )
    }



    // MARK: - 辅助方法
    private func getInstructionText() -> String {
        if viewModel.subjects.isEmpty {
            return "未检测到主体，或直接提取全部内容"
        } else if viewModel.selectedSubjectIndices.isEmpty {
            return "点击绿色圆点选择要提取的主体，或直接提取全部主体"
        } else {
            return "已选择 \(viewModel.selectedSubjectIndices.count) 个主体，点击「提取选中主体」继续"
        }
    }

    private func getProcessButtonText() -> String {
        if viewModel.selectedSubjectIndices.isEmpty {
            return "提取全部主体"
        } else {
            return "提取选中主体 (\(viewModel.selectedSubjectIndices.count))"
        }
    }


}

// MARK: - 主体选择指示器组件
struct SubjectIndicator: View {
    let subject: ImageProcess.SubjectInfo
    let isSelected: Bool
    let imageFrame: CGRect
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            ZStack {
                // 外圈
                Circle()
                    .fill(Color.green.opacity(isSelected ? 0.3 : 0.1))
                    .frame(width: 44, height: 44)
                    .overlay(
                        Circle()
                            .stroke(Color.green, lineWidth: isSelected ? 3 : 2)
                    )

                // 内圈
                Circle()
                    .fill(Color.green)
                    .frame(width: isSelected ? 16 : 12, height: isSelected ? 16 : 12)
                    .opacity(isSelected ? 1.0 : 0.6)

                // 选中标记
                if isSelected {
                    Image(systemName: "checkmark")
                        .font(.system(size: 8, weight: .bold))
                        .foregroundColor(.white)
                }
            }
        }
        .position(
            x: imageFrame.minX + subject.centerPosition.x * imageFrame.width,
            y: imageFrame.minY + subject.centerPosition.y * imageFrame.height
        )
        .scaleEffect(isSelected ? 1.1 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isSelected)
    }
}

// MARK: - 图像分析结果视图
extension ImageProcessView {
    private var imageAnalysisResultView: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.md) {
            HStack {
                Spacer()
                
                if (!viewModel.isAnalyzing && viewModel.analysisResult != nil){
                    Text(viewModel.analysisResult?.Title ?? "识别结果")
                        .font(.title3Brand)
                } else{
                    Image(systemName: "brain.head.profile")
                        .foregroundColor(.auxiliaryYellow)
                    
                    Text("图像分析")
                        .font(.headline)
                        .foregroundColor(.textPrimary)
                }

                Spacer()
            }

            if viewModel.isAnalyzing {
                HStack {
                    ProgressView()
                        .scaleEffect(0.8)
                    Text("正在分析图像内容...")
                        .font(.bodyBrand)
                        .foregroundColor(.textSecondary)
                }
                .padding(.vertical, Theme.Spacing.sm)
            } else if let result = viewModel.analysisResult {
                // 显示分析结果
                VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
                    // 描述
                    VStack(alignment: .leading, spacing: 4) {
                        Text("描述")
                            .font(.bodyBrand)
                            .foregroundColor(.textPrimary)

                        Text(result.Description)
                            .font(.bodyBrand)
                            .foregroundColor(.textSecondary)
                            .padding(.horizontal, Theme.Spacing.sm)
                            .padding(.vertical, Theme.Spacing.xs)
                            .background(Color.cardBackground.opacity(0.5))
                            .cornerRadius(Theme.CornerRadius.sm)
                    }

                    // 标签
                    VStack(alignment: .leading, spacing: 8) {
                        Text("组成标签")
                            .font(.bodyBrand)
                            .foregroundColor(.textPrimary)

                        LazyVGrid(columns: [
                            GridItem(.adaptive(minimum: 80), spacing: 8)
                        ], spacing: 8) {
                            ForEach(result.Tags, id: \.self) { tag in
                                Text(tag)
                                    .font(.captionBrand)
                                    .foregroundColor(.textPrimary)
                                    .padding(.horizontal, Theme.Spacing.sm)
                                    .padding(.vertical, 4)
                                    .background(Color.brandGreen.opacity(0.2))
                                    .cornerRadius(Theme.CornerRadius.sm)
                            }
                        }
                    }

                    // 创建卡片按钮
                    Button(action: createItemCard) {
                        HStack {
                            if isCreatingCard {
                                ProgressView()
                                    .scaleEffect(0.8)
                                    .tint(.textPrimary)
                            } else {
                                Image(systemName: "plus.rectangle.on.folder")
                                    .foregroundColor(.textPrimary)
                            }

                            Text(isCreatingCard ? "创建中..." : "创建物品卡片")
                                .font(.bodyBrand)
                                .foregroundColor(.textPrimary)
                        }
                        .frame(maxWidth: .infinity)
                    }
                    .buttonStyle(PrimaryButtonStyle())
                    .disabled(isCreatingCard)
                    .padding(.top, Theme.Spacing.sm)
                }
            } else if let error = viewModel.analysisError {
                // 显示错误信息
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Image(systemName: "exclamationmark.triangle")
                            .foregroundColor(.error)

                        Text("分析失败")
                            .font(.bodyBrand)
                            .foregroundColor(.error)
                    }
                    
                    Text(error)
                        .font(.captionBrand)
                        .foregroundColor(.error)
                        .padding(.horizontal, Theme.Spacing.sm)
                        .padding(.vertical, Theme.Spacing.xs)
                        .background(Color.cardBackground.opacity(0.5))
                        .cornerRadius(Theme.CornerRadius.sm)
                }
                .padding(.vertical, Theme.Spacing.sm)
            } else {
                // 等待分析状态
                Text("等待图像分析...")
                    .font(.bodyBrand)
                    .foregroundColor(.textSecondary)
                    .padding(.vertical, Theme.Spacing.sm)
            }
        }
        .padding(Theme.Spacing.md)
        .glassCard()
        .animation(.easeInOut(duration: 0.3), value: viewModel.isAnalyzing)
        .animation(.easeInOut(duration: 0.3), value: viewModel.analysisResult)
    }

    // MARK: - 卡片类型选择视图
    private var cardTypeSelectionView: some View {
        VStack(spacing: Theme.Spacing.xl) {
            // 标题和说明
            VStack(spacing: Theme.Spacing.md) {
                Text("确定类型")
                    .font(.title2Brand)
                    .foregroundColor(.textPrimary)

                Text("首先选择要创建的卡片类型")
                    .font(.bodyBrand)
                    .foregroundColor(.textSecondary)
                    .multilineTextAlignment(.center)
            }

            // 卡片类型选择
            VStack(spacing: Theme.Spacing.md) {
                HStack(spacing: Theme.Spacing.lg) {
                    // 风景卡片按钮
                    Button(action: {
                        viewModel.selectCardType(.scenery)
                    }) {
                        VStack(spacing: Theme.Spacing.sm) {
                            Image(systemName: "mountain.2.fill")
                                .font(.title)
                                .foregroundColor(.brandGreen)

                            Text("风景卡片")
                                .font(.bodyBrand)
                                .foregroundColor(.textPrimary)

                            Text("直接使用原图")
                                .font(.captionBrand)
                                .foregroundColor(.textSecondary)
                        }
                        .padding(Theme.Spacing.md)
                        .frame(maxWidth: .infinity)
                        .background(
                            RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                                .fill(Color.cardBackground.opacity(0.6))
                        )
                        .overlay(
                            RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                                .stroke(Color.brandGreen.opacity(0.3), lineWidth: 1)
                        )
                    }
                    .buttonStyle(PlainButtonStyle())

                    // 购物卡片按钮
                    Button(action: {
                        viewModel.selectCardType(.shopping)
                    }) {
                        VStack(spacing: Theme.Spacing.sm) {
                            Image(systemName: "bag.fill")
                                .font(.title)
                                .foregroundColor(.auxiliaryYellow)

                            Text("购物卡片")
                                .font(.bodyBrand)
                                .foregroundColor(.textPrimary)

                            Text("提取主体物品")
                                .font(.captionBrand)
                                .foregroundColor(.textSecondary)
                        }
                        .padding(Theme.Spacing.md)
                        .frame(maxWidth: .infinity)
                        .background(
                            RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                                .fill(Color.cardBackground.opacity(0.6))
                        )
                        .overlay(
                            RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                                .stroke(Color.auxiliaryYellow.opacity(0.3), lineWidth: 1)
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }

                Text("风景卡片适合记录自然景观，购物卡片适合记录商品物品")
                    .font(.captionBrand)
                    .foregroundColor(.textSecondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
            }
            .padding(Theme.Spacing.md)
        }
    }
}


// MARK: - 卡片创建成功弹窗组件

/// 卡片创建成功提示弹窗
struct CardCreatedSuccessPopup: View {
    let cardTitle: String

    var body: some View {
        VStack(spacing: Theme.Spacing.md) {
            // 成功图标
            Image(systemName: "checkmark.circle.fill")
                .font(.system(size: 40))
                .foregroundColor(.brandGreen)

            // 标题
            Text("卡片创建成功！")
                .font(.bodyBrand)
                .foregroundColor(.textPrimary)

            // 卡片标题
            Text(cardTitle)
                .font(.bodyBrand)
                .foregroundColor(.textSecondary)
                .multilineTextAlignment(.center)
                .lineLimit(2)

            // 奖励信息
            rewardContent
        }
        .padding(Theme.Spacing.lg)
        .background(
            RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                .fill(Color.cardBackground)
                .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 5)
        )
        .padding(.horizontal, Theme.Spacing.md)
    }

    // MARK: - 奖励内容视图
    private var rewardContent: some View {
        HStack(spacing: Theme.Spacing.md) {
            // 碳币奖励
            HStack(spacing: Theme.Spacing.xs) {
                Image(systemName: "bitcoinsign.circle.fill")
                    .foregroundColor(.yellow)
                    .font(.system(size: 16))

                Text("+10")
                    .font(.captionBrand)
                    .foregroundColor(.textSecondary)
            }
            .padding(.horizontal, Theme.Spacing.sm)
            .padding(.vertical, Theme.Spacing.xs)
            .background(
                RoundedRectangle(cornerRadius: Theme.CornerRadius.sm)
                    .fill(Color.yellow.opacity(0.1))
            )

            // 经验奖励
            HStack(spacing: Theme.Spacing.xs) {
                Image(systemName: "star.fill")
                    .foregroundColor(.blue)
                    .font(.system(size: 16))

                Text("+5")
                    .font(.captionBrand)
                    .foregroundColor(.textSecondary)
            }
            .padding(.horizontal, Theme.Spacing.sm)
            .padding(.vertical, Theme.Spacing.xs)
            .background(
                RoundedRectangle(cornerRadius: Theme.CornerRadius.sm)
                    .fill(Color.blue.opacity(0.1))
            )
        }
    }
}

// MARK: - 图片选择视图
extension ImageProcessView {
    private var imageSelectionView: some View {
        VStack(spacing: Theme.Spacing.xl) {
            // 步骤指示和返回按钮
            stepHeader(
                title: "选择图片",
                subtitle: "选择要处理的图片",
                showBackButton: true
            )

            // 图片获取选项
            VStack(spacing: Theme.Spacing.lg) {
                // 拍照按钮
                Button(action: {
                    Task {
                        if await viewModel.checkPhotoLibraryPermission() {
                            showCamera = true
                        }
                    }
                }) {
                    HStack(spacing: Theme.Spacing.md) {
                        Image(systemName: "camera.fill")
                            .font(.title2)
                            .foregroundColor(.brandGreen)

                        VStack(alignment: .leading, spacing: 4) {
                            Text("拍摄照片")
                                .font(.bodyBrand)
                                .foregroundColor(.textPrimary)

                            Text("使用相机拍摄新照片")
                                .font(.captionBrand)
                                .foregroundColor(.textSecondary)
                        }

                        Spacer()

                        Image(systemName: "chevron.right")
                            .foregroundColor(.textSecondary)
                    }
                    .padding(Theme.Spacing.lg)
                    .background(
                        RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                            .fill(Color.cardBackground.opacity(0.6))
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                            .stroke(Color.brandGreen.opacity(0.3), lineWidth: 1)
                    )
                }
                .buttonStyle(PlainButtonStyle())

                // 从相册选择按钮
                Button(action: {
                    Task {
                        if await viewModel.checkPhotoLibraryPermission() {
                            viewModel.showPhotoPicker = true
                        }
                    }
                }) {
                    HStack(spacing: Theme.Spacing.md) {
                        Image(systemName: "photo.fill")
                            .font(.title2)
                            .foregroundColor(.auxiliaryYellow)

                        VStack(alignment: .leading, spacing: 4) {
                            Text("从相册选择")
                                .font(.bodyBrand)
                                .foregroundColor(.textPrimary)

                            Text("从相册中选择已有照片")
                                .font(.captionBrand)
                                .foregroundColor(.textSecondary)
                        }

                        Spacer()

                        Image(systemName: "chevron.right")
                            .foregroundColor(.textSecondary)
                    }
                    .padding(Theme.Spacing.lg)
                    .background(
                        RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                            .fill(Color.cardBackground.opacity(0.6))
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                            .stroke(Color.auxiliaryYellow.opacity(0.3), lineWidth: 1)
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }
            .padding(Theme.Spacing.md)
            .glassCard()
        }
    }
}

// MARK: - 主体选择视图
extension ImageProcessView {
    private var subjectSelectionView: some View {
        VStack(spacing: Theme.Spacing.xl) {
            // 步骤指示和返回按钮
            stepHeader(
                title: "选择主体",
                subtitle: "点击绿色圆点选择要提取的主体物品",
                showBackButton: true
            )

            // 图片显示区域
            imageDisplayArea

            // 提示文本和控制按钮
            VStack(spacing: 12) {
                Text(getInstructionText())
                    .font(.captionBrand)
                    .foregroundColor(.textSecondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)

                // 选择控制按钮
                if viewModel.showSubjectSelection && !viewModel.subjects.isEmpty {
                    HStack(spacing: 16) {
                        Button("全选") {
                            viewModel.selectAllSubjects()
                        }
                        .font(.captionBrand)
                        .foregroundColor(.blue)

                        Button("清除") {
                            viewModel.clearSelection()
                        }
                        .font(.captionBrand)
                        .foregroundColor(.red)

                        Text("已选择: \(viewModel.selectedSubjectIndices.count)")
                            .font(.captionBrand)
                            .foregroundColor(.textSecondary)
                    }
                }
            }
            .padding(Theme.Spacing.md)
            .glassCard()

            // 处理按钮
            Button(action: {
                viewModel.processImage()
            }) {
                HStack {
                    if viewModel.isProcessing {
                        ProgressView()
                            .scaleEffect(0.8)
                            .tint(.textPrimary)
                    } else {
                        Image(systemName: "scissors")
                            .foregroundColor(.textPrimary)
                    }

                    Text(viewModel.isProcessing ? "处理中..." : getProcessButtonText())
                        .font(.bodyBrand)
                        .foregroundColor(.textPrimary)
                }
                .frame(maxWidth: .infinity)
            }
            .buttonStyle(PrimaryButtonStyle())
            .disabled(viewModel.inputImage == nil || viewModel.isProcessing)
        }
    }
}

// MARK: - 图像分析视图
extension ImageProcessView {
    private var imageAnalysisView: some View {
        VStack(spacing: Theme.Spacing.xl) {
            // 步骤指示和返回按钮
            stepHeader(
                title: "确认创建",
                subtitle: "确认图像识别结果并创建卡片",
                showBackButton: true
            )

            // 处理后的图片显示
            if let processedImage = viewModel.processedImage {
                Image(uiImage: processedImage)
                    .resizable()
                    .scaledToFit()
                    .frame(maxHeight: 300)
                    .cornerRadius(Theme.CornerRadius.lg)
                    .padding(Theme.Spacing.md)
                    .glassCard()
            }

            // 图像分析结果
            imageAnalysisResultView
        }
    }
}

// MARK: - 辅助视图组件
extension ImageProcessView {
    /// 步骤标题头部
    private func stepHeader(title: String, subtitle: String, showBackButton: Bool = false) -> some View {
        VStack(spacing: Theme.Spacing.sm) {
            HStack {
                if showBackButton {
                    Button(action: {
                        viewModel.goToPreviousStep()
                    }) {
                        HStack(spacing: 4) {
                            Image(systemName: "chevron.left")
                                .font(.captionBrand)
                            Text("返回")
                                .font(.captionBrand)
                        }
                        .foregroundColor(.brandGreen)
                    }
                }

                Spacer()

                // 步骤指示器
                HStack(spacing: 8) {
                    ForEach(0..<4) { index in
                        Circle()
                            .fill(index <= getCurrentStepIndex() ? Color.brandGreen : Color.gray.opacity(0.3))
                            .frame(width: 8, height: 8)
                    }
                }

                Spacer()

                if showBackButton {
                    // 占位符保持对称
                    HStack(spacing: 4) {
                        Image(systemName: "chevron.left")
                            .font(.captionBrand)
                        Text("返回")
                            .font(.captionBrand)
                    }
                    .foregroundColor(.clear)
                }
            }

            Text(title)
                .font(.title2Brand)
                .foregroundColor(.textPrimary)

            Text(subtitle)
                .font(.bodyBrand)
                .foregroundColor(.textSecondary)
                .multilineTextAlignment(.center)
        }
        .padding(Theme.Spacing.md)
        .glassCard()
    }

    /// 获取当前步骤索引
    private func getCurrentStepIndex() -> Int {
        switch viewModel.currentStep {
        case .cardTypeSelection: return 0
        case .imageSelection: return 1
        case .subjectSelection: return 2
        case .imageAnalysis: return 3
        }
    }
}

#Preview {
    ImageProcessView()
}
