//
//  ImageAPI.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/23.
//

import Foundation
import SwiftUI

// MARK: - Gemini API 请求模型
struct GeminiRequest: Encodable {
    struct Content: Encodable {
        struct Part: Encodable {
            struct InlineData: Encodable {
                let mime_type: String
                let data: String
            }
            let inline_data: InlineData?
            let text: String?
        }
        let parts: [Part]
    }
    let contents: [Content]
}

// MARK: - Gemini API 响应模型
struct GeminiResponse: Decodable {
    struct Candidate: Decodable {
        struct Content: Decodable {
            struct Part: Decodable {
                let text: String
            }
            let parts: [Part]
        }
        let content: Content
    }
    let candidates: [Candidate]
}

// MARK: - 解析的输出模型
struct ImageAnalysisResult: Decodable, Equatable {
    let Description: String
    let Title: String
    let Eco_friendly: Int
    let pack_value: Int
}

// MARK: - Gemini API 服务类
@MainActor
class GeminiImageAnalysisService: ObservableObject {
    @Published var tags: [String] = []
    @Published var description: String = ""
    @Published var title: String = ""
    @Published var isLoading = false
    @Published var errorMessage: String? = nil

    // 默认API key（开发阶段使用）
    private let defaultAPIKey = "AIzaSyCej2y_ixfxI0ZxkIKSKuoSq0m7wtoJ7XE"

    /// 分析图像内容，返回Tags和Description
    /// - Parameters:
    ///   - image: 要分析的UIImage
    ///   - customApiKey: 自定义API key，如果为nil则使用默认key
    /// - Returns: 分析结果，包含Tags和Description
    func analyzeImage(_ image: UIImage, customApiKey: String? = nil) async -> ImageAnalysisResult? {
        isLoading = true
        errorMessage = nil

        // 准备API URL
        guard let url = URL(string: "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent") else {
            errorMessage = "无效的API URL"
            isLoading = false
            return nil
        }

        // 使用自定义API key或默认key
        let apiKey = customApiKey ?? defaultAPIKey

        // 将UIImage转换为JPEG Data并编码为Base64
        guard let imageData = image.jpegData(compressionQuality: 0.8) else {
            errorMessage = "图像数据转换失败"
            isLoading = false
            return nil
        }

        let base64String = imageData.base64EncodedString()

        // 构建请求
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue(apiKey, forHTTPHeaderField: "x-goog-api-key")

        // MARK: 构建提示词
        let prompt = """
        # ROLE
        You are an expert in image content recognition and analysis.

        # GOAL
        Your task is to receive an input image that contains a main subject, identify its distinct components, and output both:
        1. A list of clearly identifiable component tags.
        2. A concise one-sentence description of the overall subject.
        3. A Title composed of Adjective plus noun.

        # EXAMPLE
        ## User's Request
        {
          "image": "{{...}}",
        }
        ## Your output
        {
          "Tags": ["塑料瓶身", "纸质杯托"],
          "Description": "这是一个赛百味品牌的杯子"
          "Title": "平平无奇的杯子"
        }

        # NOTICE
        - List only the most obvious, visually distinct components in the Tags array.
        - Keep the Description concise and informative.
        - Only output valid JSON with exactly the keys: "Tags" and "Description".
        - Do not include additional commentary, explanation, or other keys.
        - Keep your result in Chinese.
        

        Now, based on the provided image input, perform the recognition and output the result in the requested JSON format.
        """

        // 构建请求体
        let requestBody = GeminiRequest(
            contents: [
                .init(parts: [
                    .init(inline_data: .init(mime_type: "image/jpeg", data: base64String), text: nil),
                    .init(inline_data: nil, text: prompt) 
                ])
            ]
        )

        do {
            request.httpBody = try JSONEncoder().encode(requestBody)
        } catch {
            errorMessage = "请求编码失败: \(error.localizedDescription)"
            isLoading = false
            return nil
        }

        do {
        // MARK: 解析请求
            let (data, response) = try await URLSession.shared.data(for: request)

            guard let httpResponse = response as? HTTPURLResponse else {
                errorMessage = "无效的响应格式"
                isLoading = false
                return nil
            }

            guard (200...299).contains(httpResponse.statusCode) else {
                // 尝试解析错误响应以获取更多详细信息
                var errorDetail = "无详细错误信息"
                if let errorResponse = String(data: data, encoding: .utf8) {
                    errorDetail = errorResponse
                }
                errorMessage = "服务器错误: HTTP \(httpResponse.statusCode) - \(errorDetail)"
                print("Gemini API 错误详情: \(errorDetail)")
                isLoading = false
                return nil
            }

            // 解析Gemini API响应
            let geminiResponse = try JSONDecoder().decode(GeminiResponse.self, from: data)

            guard let jsonString = geminiResponse.candidates.first?.content.parts.first?.text else {
                errorMessage = "API响应中未找到文本内容"
                isLoading = false
                return nil
            }
            
            // 清理可能的 Markdown 代码块格式
            let cleanedJsonString = jsonString.trimmingCharacters(in: .whitespacesAndNewlines)
                .replacingOccurrences(of: "^```json\\s*", with: "", options: .regularExpression)
                .replacingOccurrences(of: "\\s*```$", with: "", options: .regularExpression)
            
            print("清理后的JSON字符串: \(cleanedJsonString)")

            // 二次解码JSON字符串为ImageAnalysisResult
            guard let jsonData = cleanedJsonString.data(using: .utf8),
                  let result = try? JSONDecoder().decode(ImageAnalysisResult.self, from: jsonData) else {
                errorMessage = "解析分析结果失败，API返回格式不正确"
                print("解析失败的原始响应内容: \(jsonString)")
                print("清理后的JSON字符串: \(cleanedJsonString)")
                isLoading = false
                return nil
            }

            // 更新发布的属性
            tags = result.Tags
            description = result.Description
            title = result.Title

            isLoading = false
            return result

        } catch let error as URLError where error.code == .notConnectedToInternet {
            errorMessage = "网络连接失败，请检查网络设置"
            print("网络错误: \(error.localizedDescription)")
            isLoading = false
            return nil
        } catch {
            errorMessage = "网络请求失败: \(error.localizedDescription)"
            print("网络请求错误: \(error)")
            isLoading = false
            return nil
        }
    }
}
